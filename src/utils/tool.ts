import md5 from 'md5'
import moment from 'moment'

export function md5Hash(input) {
  return md5(input).toString()
}
export function isValidPhoneNumber(phoneNumber) {
  const regex = /^1[3-9]\d{9}$/

  return regex.test(phoneNumber)
}

export function isValidEmail(email) {
  const regex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i

  return regex.test(email)
}
export function formatTime(time) {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
