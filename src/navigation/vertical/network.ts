export default [
  { heading: 'NetworkManagement' },
  {
    title: 'NetworkTopology',
    code: 'network:topology:',
    to: { name: 'network-tupo' },
    icon: { icon: 'tabler-topology-custom' },
  },
  {
    title: '网络数据统计',
    code: 'network:calc',
    icon: { icon: 'tabler-network-stats-custom' },
    children: [
      {
        title: 'AP数据',
        code: 'network:ap',
        to: { name: 'network-ap-data' },
        icon: { icon: 'tabler-event-custom' },
      },
      {
        title: '终端数据',
        code: 'network:terminal',
        to: { name: 'network-plc-data' },
        icon: { icon: 'tabler-event-custom' },
      },
    ],
  },
  {
    title: '网络概况',
    code: 'network:status',
    icon: { icon: 'tabler-network-overview-custom' },
    children: [
      {
        title: '网络状态',
        code: 'network:status-net',
        to: { name: 'network-status' },
        icon: { icon: 'tabler-event-custom' },
      },
      {
        title: '设备状态',
        code: 'network:status-device',
        to: { name: 'network-device-status' },
        icon: { icon: 'tabler-event-custom' },
      },
      {
        title: '终端状态',
        code: 'network:status-terminal',
        to: { name: 'network-plc-status' },
        icon: { icon: 'tabler-event-custom' },
      },
    ],
  },
  {
    title: 'NetworkOperations',
    code: 'network:ops',
    icon: { icon: 'tabler-network-custom' },
    children: [
      {
        title: 'NetworkEvents',
        code: 'network:event',
        to: { name: 'network-event' },
        icon: { icon: 'tabler-event-custom' },
      },
    ],
  },
]
