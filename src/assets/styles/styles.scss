// Write your overrides

.chart-tooltip {
  padding: 15px;
  background-color: rgb(var(--v-theme-surface));

  &-title {
    color: rgb(var(--v-theme-on-surface));
    font-size: 15px;
    font-weight: 500;
    margin-block-end: 12px;
  }

  &-label {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
    font-size: 12px;
  }

  &-value {
    color: rgb(var(--v-theme-on-surface));
    font-weight: 500;
    font-weight: 13px;
  }

  p {
    margin-block-end: 0;
  }
}

/* 在你的全局样式文件中，例如 main.css 或 App.vue 的 <style> 标签内 */
.el-message {
  z-index: 9999 !important; /* 根据需要调整 z-index 值 */
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
}
