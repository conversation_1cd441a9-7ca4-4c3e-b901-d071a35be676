<script lang="ts" setup>
// Components
import Footer from '@/layouts/components/Footer.vue'
import NavBarNotifications from '@/layouts/components/NavBarNotifications.vue'

// import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'
import navItems from '@/navigation/vertical'
import NavBarI18n from '@core/components/I18n.vue'

// @layouts plugin
import { VerticalNavLayout } from '@layouts'
import { themeConfig } from '@themeConfig'

const navList = reactive<typeof navItems>([])
const requestIndex = ref(0)

const getList = async () => {
  let md5Str = ''
  let token = sessionStorage.getItem('token')

  if (!token) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    token = sessionStorage.getItem('token')
  }

  if (requestIndex.value === 0)
    md5Str = ''
  else
    md5Str = localStorage.getItem('md5') || ''

  const res: any = await $get('/v1/myPermissions', {
    md5: md5Str,
  })

  requestIndex.value++
  let arr = ['dash:board:', 'network:calc:', 'network:ops:', 'network:status:', 'dev:create']
  if (res?.msg === 'success') {
    localStorage.setItem('md5', res.result.md5)
    arr = arr.concat(res.result.permissions)
  }

  const tempMap: Record<string, Record<string, boolean>> = {}
  const allActions = new Set<string>()

  arr.forEach((item: string) => {
    const [module, submodule, action] = item.split(':')
    const title = `${module}:${submodule}`
    if (!tempMap[title])
      tempMap[title] = {}

    tempMap[title][action] = true
    allActions.add(action)
  })

  const actionsArray = Array.from(allActions)

  const list = Object.entries(tempMap).map(([title, actions]) => {
    const filledActions: Record<string, boolean> = {}

    actionsArray.forEach(action => {
      filledActions[action] = !!actions[action]
    })

    return { title, actions: filledActions }
  })

  // 检查项目是否在权限列表中
  const hasPermission = (item: any) => {
    return list.some(menuItem => menuItem.title === item.code)
  }

  // 获取分组下的所有可见子项
  const getVisibleChildren = (startIndex: number) => {
    const children = []
    for (let i = startIndex; i < navItems.length; i++) {
      const item = navItems[i]
      if ('heading' in item)
        break

      // 遇到下一个分组标题就停止
      if (hasPermission(item)) {
        children.push({
          item,
          index: i,
        })
      }
    }

    return children
  }

  // 根据 list 中的 title 动态调整路由配置
  const filteredNavItems = []
  const processedIndices = new Set()

  for (let i = 0; i < navItems.length; i++) {
    if (processedIndices.has(i))
      continue

    const item = navItems[i]

    if ('heading' in item) {
      // 获取该分组下的所有可见子项
      const visibleChildren = getVisibleChildren(i + 1)

      // 只有当有可见子项时才添加分组标题
      if (visibleChildren.length > 0) {
        filteredNavItems.push(item)

        // 添加可见子项并标记已处理
        visibleChildren.forEach(({ item: childItem, index }) => {
          filteredNavItems.push(childItem)
          processedIndices.add(index)
        })
      }
    }
    else if (hasPermission(item) && !processedIndices.has(i)) {
      filteredNavItems.push(item)
      processedIndices.add(i)
    }
  }

  // 更新导航列表
  navList.length = 0
  navList.push(...filteredNavItems)
}

onMounted(() => {
  getList()
})
</script>

<template>
  <VerticalNavLayout :nav-items="navList">
    <!-- 👉 navbar -->
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div class="d-flex h-100 align-center">
        <IconBtn
          id="vertical-nav-toggle-btn"
          class="ms-n3 d-lg-none"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon
            icon="tabler-menu-2"
            size="26"
          />
        </IconBtn>

        <VSpacer />
        <!-- 中英文切换 -->
        <NavBarI18n
          v-if="themeConfig.app.i18n.enable && themeConfig.app.i18n.langConfig?.length"
          :languages="themeConfig.app.i18n.langConfig"
        />
        <!-- 主题切换 -->
        <!--        <NavbarThemeSwitcher /> -->
        <NavBarNotifications class="me-1" />
        <UserProfile />
      </div>
    </template>

    <!-- 👉 Pages -->
    <slot />

    <!-- 👉 Footer -->
    <template #footer>
      <Footer />
    </template>

    <!-- 👉 Customizer -->
    <!-- <TheCustomizer /> -->
  </VerticalNavLayout>
</template>
