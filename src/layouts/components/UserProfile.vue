<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()

const handleLogout = () => {
  try {
    // 保存需要保留的值
    const username = localStorage.getItem('username')
    const password = localStorage.getItem('password')
    const remember = localStorage.getItem('remember')

    // 清除localStorage中除了username、password和remember之外的所有项
    const keysToKeep = ['username', 'password', 'remember']

    Object.keys(localStorage).forEach(key => {
      if (!keysToKeep.includes(key))
        localStorage.removeItem(key)
    })

    // 清除所有sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      sessionStorage.removeItem(key)
    })

    // 如果需要，重新设置保留的值
    if (!localStorage.getItem('username') && username)
      localStorage.setItem('username', username)
    if (!localStorage.getItem('password') && password)
      localStorage.setItem('password', password)
    if (!localStorage.getItem('remember') && remember)
      localStorage.setItem('remember', remember)
  }
  catch (error) {
  }

  // 跳转到登录页面
  router.push('/login')
}
</script>

<template>
  <VAvatar
    class="cursor-pointer"
    color="#ffffff"
    variant="tonal"
  >
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.2"
        d="M11 2.00269C9.17943 2.00215 7.40153 2.55393 5.90115 3.58512C4.40078 4.61632 3.24853 6.07841 2.59662 7.77826C1.9447 9.47811 1.8238 11.3357 2.24987 13.1057C2.67595 14.8758 3.62896 16.4749 4.98301 17.6918V17.6918C5.54721 16.5808 6.40804 15.6477 7.47009 14.9959C8.53213 14.3441 9.7539 13.9991 11 13.9992C10.2585 13.9992 9.53372 13.7793 8.91722 13.3674C8.30072 12.9554 7.82021 12.3699 7.53646 11.6849C7.25272 10.9999 7.17848 10.2461 7.32313 9.5189C7.46778 8.79168 7.82483 8.12369 8.34912 7.5994C8.87342 7.07511 9.54141 6.71806 10.2686 6.5734C10.9958 6.42875 11.7496 6.50299 12.4346 6.78674C13.1197 7.07048 13.7052 7.55099 14.1171 8.16749C14.529 8.784 14.7489 9.50881 14.7489 10.2503C14.7489 11.2445 14.3539 12.1981 13.6509 12.9011C12.9478 13.6042 11.9943 13.9992 11 13.9992C12.2461 13.9991 13.4679 14.3441 14.5299 14.9959C15.592 15.6477 16.4528 16.5808 17.017 17.6918C18.371 16.4749 19.3241 14.8758 19.7501 13.1057C20.1762 11.3357 20.0553 9.47811 19.4034 7.77826C18.7515 6.07841 17.5992 4.61632 16.0988 3.58512C14.5985 2.55393 12.8206 2.00215 11 2.00269Z"
        fill="#2F2B3D"
        fill-opacity="0.9"
      />
      <path
        d="M10.9993 13.9992C13.0698 13.9992 14.7482 12.3207 14.7482 10.2503C14.7482 8.17981 13.0698 6.50137 10.9993 6.50137C8.92886 6.50137 7.25042 8.17981 7.25042 10.2503C7.25042 12.3207 8.92886 13.9992 10.9993 13.9992ZM10.9993 13.9992C9.75317 13.9992 8.53135 14.3437 7.4693 14.9956C6.40725 15.6474 5.54646 16.5807 4.98233 17.6918M10.9993 13.9992C12.2455 13.9992 13.4673 14.3437 14.5293 14.9956C15.5914 15.6474 16.4522 16.5807 17.0163 17.6918M19.9967 11.0001C19.9967 15.9692 15.9684 19.9974 10.9993 19.9974C6.03021 19.9974 2.00195 15.9692 2.00195 11.0001C2.00195 6.03094 6.03021 2.00269 10.9993 2.00269C15.9684 2.00269 19.9967 6.03094 19.9967 11.0001Z"
        stroke="#2F2B3D"
        stroke-opacity="0.9"
        stroke-width="1.19965"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>

    <!-- SECTION Menu -->
    <VMenu
      activator="parent"
      width="230"
      location="bottom end"
      offset="14px"
    >
      <VList>
        <!-- 👉 Logout -->
        <VListItem @click="handleLogout">
          <template #prepend>
            <VIcon
              class="me-2"
              icon="tabler-logout"
              size="22"
            />
          </template>
          <VListItemTitle>{{ t('Logout') }}</VListItemTitle>
        </VListItem>
      </VList>
    </VMenu>
    <!-- !SECTION -->
  </VAvatar>
</template>
