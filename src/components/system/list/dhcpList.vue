<script lang="ts" setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

onMounted(() => {
  getDhcpList()
})

const dhcpList: any = ref([])

async function getDhcpList() {
  const data = await $post('', { requestType: 602 })
  if (data.err_code == 0) {
    if (data.info.dhcpStatic != undefined && data.info.dhcpStatic != '')
      dhcpList.value = data.info.dhcpStatic
    else
      dhcpList.value = []
  }
}

// 显式暴露 getPortList 方法给父组件
defineExpose({
  getDhcpList, // 父组件可通过 ref 访问 this.$refs.child.getPortList()
})

const delItem = async (index: number) => {
  const data = await $post('', {
    requestType: 600,
    data: {
      dhcp: {
        static_index: `${index}`,
      },
    },
  })

  if (data.err_code == 0)
    getDhcpList()
}

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.25rem;' : 'padding-block: 1.25rem;'
</script>

<template>
  <VCard>
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>{{ t('NetworkConfig.DHCP.DeviceName') }}</th>
          <th>{{ t('NetworkConfig.DHCP.IPAddress') }}</th>
          <th>{{ t('NetworkConfig.DHCP.MACAddress') }}</th>
          <th>{{ t('Action') }}</th>
        </tr>
      </thead>

      <tbody>
        <tr
          v-for="(dhcpItem, index) in dhcpList"
          :key="index"
        >
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ dhcpItem.hostName }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ dhcpItem.ipAddress }}
            </div>
          </td>

          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ dhcpItem.macAddress }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="flexBox">
              <VBtn
                variant="text"
                size="small"
                color="error"
                @click="delItem(index)"
              >
                {{ t('Delete') }}
              </VBtn>
            </div>
          </td>
        </tr>
      </tbody>
    </VTable>
  </VCard>
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}

.download {
  color: var(--Color-Primary-primary-500, #4080ff);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}

.upload {
  color: var(--Color-Success-success-500, #28c76f);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}

.flexBox {
  display: flex;
  align-items: center;
}

.text-red {
  color: #ff7074 !important;
}
</style>
