<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

onMounted(() => {
  getListIp()
})

// 方式1：通过 globalProperties
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

const lastTransitions: any = ref([])
async function getListIp() {
  const data = await $post('', { requestType: 251 })
  if (data.err_code == 0) {
    const info: any = {
      systemMapLanIP: '',
      systemMapAccessIP: '',
      systemMapInternalIP: '',
      systemMapHostIP: '',
    }

    if (data.info.systemMapIPDisable == '0') {
      if (data.info.systemMapLanIP != undefined)
        info.systemMapLanIP = data.info.systemMapLanIP

      if (data.info.systemMapAccessIP != undefined)
        info.systemMapAccessIP = data.info.systemMapAccessIP

      if (data.info.systemMapInternalIP != undefined)
        info.systemMapInternalIP = data.info.systemMapInternalIP

      if (data.info.systemMapHostIP != undefined)
        info.systemMapHostIP = data.info.systemMapHostIP

      lastTransitions.value = [info]
      console.log(lastTransitions.value, 98999)
    }
    else {
      lastTransitions.value = []
    }
  }
}

const delItem = async (IPInfo: any) => {
  const data = await $post('', {
    requestType: 252,
    data: {
      map_disable: '1',
      map_lan_ip: IPInfo.systemMapLanIP,
      wan_host_ip: IPInfo.systemMapAccessIP,
      access_wan_ip: IPInfo.systemMapHostIP,
    },
  })

  if (data.err_code == 0)
    getWaittingModal(6)
  else
    getWaittingModal(9999)
}

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.25rem;' : 'padding-block: 1.25rem;'
</script>

<template>
  <VCard>
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>{{ t('NetworkConfig.IP.DeviceIP') }}</th>
          <th>{{ t('NetworkConfig.IP.MappingIP') }}</th>
          <th>{{ t('NetworkConfig.IP.CommunicationHostIP') }}</th>
          <th>{{ t('NetworkConfig.IP.InternalIP') }}</th>
          <th>{{ t('Action') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(transition, index) in lastTransitions"
          :key="index"
        >
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.systemMapLanIP || '--' }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.systemMapAccessIP || '--' }}
            </div>
          </td>

          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.systemMapHostIP || '--' }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.systemMapInternalIP || '--' }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <VBtn
              variant="text"
              color="error"
              size="small"
              @click="delItem(transition)"
            >
              {{ t('NetworkConfig.IP.Close') }}
            </VBtn>
          </td>
        </tr>
      </tbody>
    </VTable>
  </VCard>
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}

.download {
  color: var(--Color-Primary-primary-500, #4080ff);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;

  /* 138.462% */
  letter-spacing: 0.4px;
  line-height: 18px;
}

.upload {
  color: var(--Color-Success-success-500, #28c76f);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;

  /* 138.462% */
  letter-spacing: 0.4px;
  line-height: 18px;
}

.text-base {
  color: #ff7074 !important;
}
</style>
