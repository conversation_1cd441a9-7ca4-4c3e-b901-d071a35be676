<script lang="ts" setup>
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import IPTable from '@/components/system/list/IPTable.vue'

const { t } = useI18n()

const ipForm = ref()

// 方式1：通过 globalProperties
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

onMounted(() => {
  getMapIp()
})

const IPInfo: any = reactive({})

async function getMapIp() {
  const data = await $post('', { requestType: 251 })
  if (data.err_code == 0) {
    if (data.info.systemMapIPDisable != undefined) {
      if (data.info.systemMapIPDisable == '0') {
        IPInfo.disable = data.info.systemMapIPDisable
        if (data.info.systemMapLanIP != undefined)
          IPInfo.systemMapLanIP = data.info.systemMapLanIP

        if (data.info.systemMapAccessIP != undefined)
          IPInfo.systemMapAccessIP = data.info.systemMapAccessIP

        if (data.info.systemMapInternalIP != undefined)
          IPInfo.systemMapInternalIP = data.info.systemMapInternalIP

        if (data.info.systemMapHostIP != undefined)
          IPInfo.systemMapHostIP = data.info.systemMapHostIP
      }
      else {
        IPInfo.disable = data.info.systemMapIPDisable
        if (data.info.systemMapLanIP != undefined)
          IPInfo.systemMapLanIP = data.info.systemMapLanIP

        if (data.info.systemMapAccessIP != undefined)
          IPInfo.systemMapAccessIP = data.info.systemMapAccessIP

        if (data.info.systemMapInternalIP != undefined)
          IPInfo.systemMapInternalIP = data.info.systemMapInternalIP

        if (data.info.systemMapHostIP != undefined)
          IPInfo.systemMapHostIP = data.info.systemMapHostIP
      }
    }
  }
}

const submitIP = async () => {
  ipForm.value.validate().then(async (result: any) => {
    if (result.valid) {
      const data = await $post('', {
        requestType: 252,
        data: {
          map_disable: '0',
          map_lan_ip: IPInfo.systemMapLanIP,
          wan_host_ip: IPInfo.systemMapHostIP,
          access_wan_ip: IPInfo.systemMapAccessIP,
        },
      })

      if (data.err_code == 0)
        getWaittingModal(6)
      else
        getWaittingModal(9999)
    }
  })
}

const ipValidator = (value: string) => {
  // IPv4 校验
  const ipv4Regex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/
  if (!ipv4Regex.test(value))
    return t('NetworkConfig.IP.InvalidIP')

  return true
}

const ipValidatorNew = (value: string) => {
  if (!value)
    return true

  // IPv4 校验
  const ipv4Regex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/
  if (!ipv4Regex.test(value))
    return t('NetworkConfig.IP.InvalidIP')

  return true
}

const requiredValidator = (value: string) => {
  if (!value)
    return t('NetworkConfig.IP.InvalidIP')

  return true
}
</script>

<template>
  <VForm ref="ipForm">
    <VCard
      class="mb-5"
      :title="t('NetworkConfig.IP.Title')"
    >
      <VCardText>
        <div class="d-flex align-start mt-2 bg-primary-transparent pa-3 rounded border border-primary">
          <VIcon
            icon="tabler-alert-circle"
            class="mr-6"
          />
          <ol class="text-primary text-subtitle-1">
            <div>{{ t('NetworkConfig.IP.Description') }}</div>
            <li>{{ t('NetworkConfig.IP.Points.Point1') }}</li>
            <li>{{ t('NetworkConfig.IP.Points.Point2') }}</li>
            <li>{{ t('NetworkConfig.IP.Points.Point3') }}</li>
          </ol>
        </div>
        <VCard
          class="mt-6"
          :title="t('NetworkConfig.IP.AddIPMapping')"
        >
          <VCardText>
            <VRow class="match-height">
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="IPInfo.systemMapLanIP"
                  :label="t('NetworkConfig.IP.DeviceIP')"
                  :placeholder="t('NetworkConfig.IP.EnterDeviceIP')"
                  :rules="[requiredValidator, ipValidator]"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="IPInfo.systemMapAccessIP"
                  :label="t('NetworkConfig.IP.MappingIP')"
                  :placeholder="t('NetworkConfig.IP.EnterMappingIP')"
                  :rules="[requiredValidator, ipValidator]"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="IPInfo.systemMapHostIP"
                  :label="t('NetworkConfig.IP.CommunicationHostIP')"
                  :placeholder="t('NetworkConfig.IP.EnterCommunicationHostIP')"
                  :rules="[ipValidatorNew]"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="IPInfo.systemMapInternalIP"
                  readonly
                  :label="t('NetworkConfig.IP.InternalIP')"
                  :placeholder="t('NetworkConfig.IP.EnterInternalIP')"
                />
              </VCol>
            </VRow>
            <div
              class="mt-2"
              style="display: flex;justify-content: flex-end;"
            >
              <VBtn
                color="primary"
                @click="submitIP"
              >
                {{ t('NetworkConfig.IP.Apply') }}
              </VBtn>
            </div>
          </VCardText>
        </VCard>
      </VCardText>
    </VCard>
  </VForm>
  <VCard class="mb-5">
    <VCardText>
      <IPTable :list="IPTable" />
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
.blueArea {
  p {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    /* 160% */
    margin-block-end: 0;
  }

  .bold {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .text {
    position: relative;
  }

  .text::before {
    position: absolute;
    border-radius: 3px;
    block-size: 6px;
    content: "";
    inline-size: 6px;
    inset-block-start: 9px;
    inset-inline-start: 9px;
  }
}
</style>
