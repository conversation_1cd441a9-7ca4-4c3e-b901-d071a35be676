<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  systemMode: string
}>()

const { t } = useI18n()

// 方式1：通过 globalProperties
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

const wanLan = ref(false)
const wanAll = ref(false)
const lanAll = ref(false)
const typeList: any = ref([])

onMounted(async () => {
  const res = await $post('', { requestType: 201 })
  if (res.msg === 'success') {
    const data = res

    console.log(res.info)
    if (data.info.lan != undefined && data.info.wan != undefined) {
      if (props.systemMode == '0') {
        if (data.info.wan.wanIfname == 'lan1 lan2 lan3 lan4' && data.info.lan.lanIfname == 'wan') {
          wanLan.value = true
          show_lan_wan()
        }
        else if (data.info.lan.lanIfname.includes('wan') && data.info.lan.lanIfname.includes('lan1 lan2 lan3 lan4')) {
          lanAll.value = true
          show_lan_lan()
        }
        else if (data.info.wan.wanIfname.includes('wan') && data.info.wan.wanIfname.includes('lan1 lan2 lan3 lan4')) {
          wanAll.value = true
          show_wan_wan()
        }
        else {
          wanLan.value = false
          lanAll.value = false
          wanAll.value = false
          show_wan_lan()
        }
      }
      else {
        lanAll.value = true
        show_lan_lan()
      }
      getPortInfo()
    }
  }
})

function exchange_wan_lan() {
  if (wanLan.value) {
    show_lan_wan()
    wanAll.value = false
    lanAll.value = false
  }
  else {
    show_wan_lan()
  }
}

function set_all_lan() {
  if (lanAll.value) {
    show_lan_lan()
    wanAll.value = false
    wanLan.value = false
  }
  else {
    if (wanLan.value)
      show_lan_wan()
    else
      show_wan_lan()
  }
}

function set_all_wan() {
  if (wanAll.value) {
    show_wan_wan()
    wanLan.value = false
    lanAll.value = false
  }
  else {
    if (wanLan.value)
      show_lan_wan()
    else
      show_wan_lan()
  }
}

// 生成 typeStr 逻辑
const generateTypeStr = (types: string[]) => {
  const lanCount = types.filter(type => type === 'lan').length
  const wanCount = types.filter(type => type === 'wan').length

  // 全部是 lan
  if (lanCount === 5 && wanCount === 0)
    return ['lan1', 'lan2', 'lan3', 'lan4', 'lan5']

  // 全部是 wan
  if (wanCount === 5 && lanCount === 0)
    return ['wan1', 'wan2', 'wan3', 'wan4', 'wan5']

  // 混合情况：按顺序分配编号
  const result: string[] = []
  let lanIndex = 1
  let wanIndex = 1

  types.forEach(type => {
    if (type === 'lan') {
      result.push(`lan${lanIndex}`)
      lanIndex++
    }
    else if (type === 'wan') {
      result.push(`wan${wanIndex}`)
      wanIndex++
    }
  })

  return result
}

const applyChange = async (e: any) => {
  if (e.type == 0) {
    lanAll.value = !lanAll.value
    if (lanAll.value) {
      wanLan.value = false
      wanAll.value = false
    }
    set_all_lan()
  }
  if (e.type == 1) {
    wanLan.value = !wanLan.value
    if (wanLan.value) {
      lanAll.value = false
      wanAll.value = false
    }
    exchange_wan_lan()
  }
  if (e.type == 2) {
    wanAll.value = !wanAll.value
    if (wanAll.value) {
      wanLan.value = false
      lanAll.value = false
    }
    set_all_wan()
  }
  const list = dataList.value
  const typeStrArray = generateTypeStr(typeList.value)

  for (const index in list) {
    list[index].type = typeList.value[index]
    list[index].typeStr = typeStrArray[index] || ''
  }
  dataList.value = list
}

async function save() {
  let wan_ifname = 'wan'
  let lan_ifname = 'lan1 lan2 lan3 lan4'
  if (lanAll.value) {
    wan_ifname = ''
    lan_ifname = 'wan lan1 lan2 lan3 lan4'
  }
  else if (wanAll.value) {
    wan_ifname = 'wan lan1 lan2 lan3 lan4'
    lan_ifname = ''
  }
  else if (wanLan.value) {
    wan_ifname = 'lan1 lan2 lan3 lan4'
    lan_ifname = 'wan'
  }
  else { /* empty */ }

  const data = await $post('', {
    requestType: 102,
    data: {
      wan: {
        wanIfname: wan_ifname,
      },
      lan: {
        lanIfname: lan_ifname,
      },
    },
  })

  //  console.log(JSON.stringify(data));
  if (data.err_code == 0 || data.err_code == -14) {
    if (data.err_code == 0)
      getWaittingModal(5)
    else
      getWaittingModal(4)
  }
  else if (data.err_code == -20) {
    ElMessage.error(t('NetworkConfig.Network.RelayModeError'))
  }
  else {
    ElMessage.error(t('NetworkConfig.Network.Error'))
  }
}

function show_wan_lan() {
  typeList.value = ['wan', 'lan', 'lan', 'lan', 'lan']
}

function show_lan_wan() {
  typeList.value = ['lan', 'wan', 'wan', 'wan', 'wan']
}

function show_lan_lan() {
  typeList.value = ['lan', 'lan', 'lan', 'lan', 'lan']
}

function show_wan_wan() {
  typeList.value = ['wan', 'wan', 'wan', 'wan', 'wan']
}

const dataList: any = ref([])

const getPortInfo = async () => {
  try {
    const res = await $post('', { requestType: 701 })

    if (res?.err_code === 0 && res?.info?.switch0) {
      const { P1, P2, P3, P4, P5 } = res.info.switch0
      const ports = [P5, P4, P3, P2, P1]

      const typeStrArray = generateTypeStr(typeList.value)

      const list = ports
        .filter(port => port?.[port.length - 1]) // 过滤有值的端口
        .map((port, index) => ({
          type: typeList.value[index],
          typeStr: typeStrArray[index] || '',
          ...port[port.length - 1],
        }))

      console.log(list)
      dataList.value = list
    }
  }
  catch (error) {
    console.error('API 请求失败:', error)

    // 可根据需要添加错误处理逻辑
  }
}

// 统计type数量
const typeCountMap = computed(() => {
  const map = {} as Record<string, number>

  dataList.value.forEach(item => {
    map[item.type] = (map[item.type] || 0) + 1
  })

  return map
})

// 唯一type
const uniqueType = computed(() => {
  const map = typeCountMap.value
  const unique = Object.keys(map).find(type => map[type] === 1)

  // 只在有一个唯一type且其它type数量为4时才生效
  if (unique && Object.values(map).includes(4))
    return unique

  return null
})
</script>

<template>
  <VCard class="mb-5">
    <VCardText>
      <div
        class="lanBox"
        style="display: flex;align-items: center;justify-content: center;"
      >
        <div
          v-for="(item, index) in dataList"
          :class="[uniqueType && item.type === uniqueType ? 'flexBox mr-4 ml-2' : 'flexBox ml-2']"
        >
          <p
            v-if="item.speed.indexOf('2500') > -1"
            class="port"
          >
            <svg
              fill="none"
              height="48"
              viewBox="0 0 48 48"
              width="48"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H44C46.2091 0 48 1.79086 48 4V44C48 46.2091 46.2091 48 44 48H4C1.79086 48 0 46.2091 0 44V4Z"
                fill="#F8F7FA"
              />
              <path
                clip-rule="evenodd"
                d="M16.8447 9.58398C16.5154 9.58398 16.2485 9.85094 16.2485 10.1803V12.5653H13.8634C13.5341 12.5653 13.2672 12.8323 13.2672 13.1616V16.5405H8.59627C8.26696 16.5405 8 16.8075 8 17.1368V35.82C8 36.1493 8.26696 36.4163 8.59627 36.4163H39.4037C39.733 36.4163 40 36.1493 40 35.82V17.1368C40 16.8075 39.733 16.5405 39.4037 16.5405H34.733V13.1616C34.733 12.8323 34.466 12.5653 34.1367 12.5653H31.7516V10.1803C31.7516 9.85095 31.4846 9.58398 31.1553 9.58398H16.8447Z"
                fill="#165DFF"
                fill-rule="evenodd"
              />
            </svg>
          </p>
          <p
            v-else-if="item.speed.indexOf('1000') > -1"
            class="port"
          >
            <svg
              fill="none"
              height="48"
              viewBox="0 0 48 48"
              width="48"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H44C46.2091 0 48 1.79086 48 4V44C48 46.2091 46.2091 48 44 48H4C1.79086 48 0 46.2091 0 44V4Z"
                fill="#F8F7FA"
              />
              <path
                clip-rule="evenodd"
                d="M16.8447 9.58398C16.5154 9.58398 16.2485 9.85094 16.2485 10.1803V12.5653H13.8634C13.5341 12.5653 13.2672 12.8323 13.2672 13.1616V16.5405H8.59627C8.26696 16.5405 8 16.8075 8 17.1368V35.82C8 36.1493 8.26696 36.4163 8.59627 36.4163H39.4037C39.733 36.4163 40 36.1493 40 35.82V17.1368C40 16.8075 39.733 16.5405 39.4037 16.5405H34.733V13.1616C34.733 12.8323 34.466 12.5653 34.1367 12.5653H31.7516V10.1803C31.7516 9.85095 31.4846 9.58398 31.1553 9.58398H16.8447Z"
                fill="#28C76F"
                fill-rule="evenodd"
              />
            </svg>
          </p>
          <p
            v-else-if="item.speed.indexOf('100') > -1"
            class="port"
          >
            <svg
              fill="none"
              height="48"
              viewBox="0 0 48 48"
              width="48"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H44C46.2091 0 48 1.79086 48 4V44C48 46.2091 46.2091 48 44 48H4C1.79086 48 0 46.2091 0 44V4Z"
                fill="#F8F7FA"
              />
              <path
                clip-rule="evenodd"
                d="M16.8447 9.58398C16.5154 9.58398 16.2485 9.85094 16.2485 10.1803V12.5653H13.8634C13.5341 12.5653 13.2672 12.8323 13.2672 13.1616V16.5405H8.59627C8.26696 16.5405 8 16.8075 8 17.1368V35.82C8 36.1493 8.26696 36.4163 8.59627 36.4163H39.4037C39.733 36.4163 40 36.1493 40 35.82V17.1368C40 16.8075 39.733 16.5405 39.4037 16.5405H34.733V13.1616C34.733 12.8323 34.466 12.5653 34.1367 12.5653H31.7516V10.1803C31.7516 9.85095 31.4846 9.58398 31.1553 9.58398H16.8447Z"
                fill="#FF9F43"
                fill-rule="evenodd"
              />
            </svg>
          </p>
          <p
            v-else
            class="port"
          >
            <svg
              fill="none"
              height="48"
              viewBox="0 0 48 48"
              width="48"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H44C46.2091 0 48 1.79086 48 4V44C48 46.2091 46.2091 48 44 48H4C1.79086 48 0 46.2091 0 44V4Z"
                fill="#F8F7FA"
              />
              <path
                clip-rule="evenodd"
                d="M16.8447 9.58398C16.5154 9.58398 16.2485 9.85094 16.2485 10.1803V12.5653H13.8634C13.5341 12.5653 13.2672 12.8323 13.2672 13.1616V16.5405H8.59627C8.26696 16.5405 8 16.8075 8 17.1368V35.82C8 36.1493 8.26696 36.4163 8.59627 36.4163H39.4037C39.733 36.4163 40 36.1493 40 35.82V17.1368C40 16.8075 39.733 16.5405 39.4037 16.5405H34.733V13.1616C34.733 12.8323 34.466 12.5653 34.1367 12.5653H31.7516V10.1803C31.7516 9.85095 31.4846 9.58398 31.1553 9.58398H16.8447Z"
                fill="#2F2B3D"
                fill-opacity="0.16"
                fill-rule="evenodd"
              />
            </svg>
          </p>
          <div>{{ item.typeStr }}</div>
        </div>
      </div>
      <div class="list">
        <div class="itemBox">
          <div class="label">
            <svg
              fill="none"
              height="12"
              viewBox="0 0 12 12"
              width="12"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H8C10.2091 0 12 1.79086 12 4V8C12 10.2091 10.2091 12 8 12H4C1.79086 12 0 10.2091 0 8V4Z"
                fill="#165DFF"
              />
            </svg>
          </div>
          <div class="content t-blue">
            {{ t('NetworkConfig.WAN.Speeds.2500M') }}
          </div>
        </div>
        <div class="itemBox">
          <div class="label">
            <svg
              fill="none"
              height="12"
              viewBox="0 0 12 12"
              width="12"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H8C10.2091 0 12 1.79086 12 4V8C12 10.2091 10.2091 12 8 12H4C1.79086 12 0 10.2091 0 8V4Z"
                fill="#28C76F"
              />
            </svg>
          </div>
          <div class="content t-green">
            {{ t('NetworkConfig.WAN.Speeds.1000M') }}
          </div>
        </div>
        <div class="itemBox">
          <div class="label">
            <svg
              fill="none"
              height="12"
              viewBox="0 0 12 12"
              width="12"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H8C10.2091 0 12 1.79086 12 4V8C12 10.2091 10.2091 12 8 12H4C1.79086 12 0 10.2091 0 8V4Z"
                fill="#FF9F43"
              />
            </svg>
          </div>
          <div class="content t-orange">
            {{ t('NetworkConfig.WAN.Speeds.10_100M') }}
          </div>
        </div>
        <div class="itemBox">
          <div class="label">
            <svg
              fill="none"
              height="12"
              viewBox="0 0 12 12"
              width="12"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 4C0 1.79086 1.79086 0 4 0H8C10.2091 0 12 1.79086 12 4V8C12 10.2091 10.2091 12 8 12H4C1.79086 12 0 10.2091 0 8V4Z"
                fill="#2F2B3D"
                fill-opacity="0.3"
              />
            </svg>
          </div>
          <div class="content t-hui">
            {{ t('NetworkConfig.WAN.NotConnected') }}
          </div>
        </div>
      </div>
    </VCardText>
  </VCard>
  <WanList
    :list="dataList"
    @wan-lan="applyChange"
    @save="save"
  />
</template>

<style lang="scss" scoped>
@use "@core/scss/template/libs/apex-chart";

.infoLabel {
  font-family: "PingFang SC";
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20px;
  vertical-align: middle;
}

.infoContent {
  font-family: "PingFang SC";
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 22px;
  vertical-align: middle;
}

.netMain {
  background: var(--Misc-body-bg, #f8f7fa);
  inline-size: 100%;
}

.netBox {
  display: flex;
  align-items: center;
  justify-content: center;
  block-size: 140px;
  inline-size: 350px;
  margin-block: 0;
  margin-inline: auto;

  .netUp {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    inline-size: 100px;

    .netImg {
      block-size: 44px;
      inline-size: 44px;
      inset-inline-start: 28px;
    }

    .netDesc {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .netIP {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .netText {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .text-blue {
      color: var(--Color-Primary-primary-400, #6aa1ff);
    }

    .text-green {
      color: var(--Color-Palette-success-main, #28c76f);
    }
  }

  .netLine {
    flex: 1;
    margin-block: 0;
    margin-inline: 20px;
  }
}

.list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block-start: 40px;

  .itemBox {
    display: flex;
    margin-inline-end: 32px;

    .label {
      block-size: 12px;
      inline-size: 12px;
      margin-inline-end: 4px;

      svg {
        block-size: 12px;
        inline-size: 12px;
      }
    }

    .t-blue {
      color: var(--Color-Primary-primary-400, #165dff);
    }

    .t-green {
      color: var(--Color-Primary-primary-400, #28c76f);
    }

    .t-orange {
      color: var(--Color-Primary-primary-400, #ff9f43);
    }

    .t-hui {
      color: var(--Color-Primary-primary-400, rgba(47, 43, 61, 90%));
    }

    .t-poe {
      color: var(--Color-Primary-primary-400, rgba(47, 43, 61, 90%));
    }
  }
}

.flexBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.port {
  margin-block-end: 0;
}
</style>
