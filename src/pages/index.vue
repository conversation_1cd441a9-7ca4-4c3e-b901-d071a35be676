<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import AnalyticsCountCloud from '@/components/analytics/AnalyticsCountCloud.vue'
import AnalyticsSupportTracker from '@/components/analytics/AnalyticsSupportTracker.vue'
import ApexChartAreaChart from '@/components/analytics/ApexChartAreaChart.vue'

const { t } = useI18n()
</script>

<template>
  <VRow class="match-height">
    <VCol cols="12">
      <AnalyticsCountCloud />
    </VCol>
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('BandwidthUsage') }}</VCardTitle>
          <VCardSubtitle>{{ t('TotalUsageAllDevices') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <ApexChartAreaChart />
        </VCardText>
      </VCard>
    </VCol>

    <VCol
      cols="12"
      md="6"
    >
      <AnalyticsSupportTracker />
    </VCol>
    <VCol cols="12">
      <AnalyticsProjectTable />
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart";
</style>
