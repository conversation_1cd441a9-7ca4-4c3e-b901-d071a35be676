<script lang="ts" setup>
interface TerminalInfo {
  mac: string
  rssi: number
  tx_rate: number
  rx_rate: number
  access_way: string
  snr: string
  chan: number
  ht: string
  max_rate: string
  assoc_time: string
  connect_time: string
  ip: string
  name: string
  ssid: string

}

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 0,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 1,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 2,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 3,
    },
  ],
})

const headers = [
  { title: '事件等级', key: 'status' },
  { title: '事件类型', key: 'ip' },
  { title: '事件名称', key: 'no' },
  { title: '发生时间', key: 'time' },
  { title: '事件状态', key: 'no' },
  { title: '操作', key: 'detail' },
]

const eventClass = {
  0: 'default',
  1: 'info',
  2: 'warning',
  3: 'error',
}

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const balanceChartConfig = computed(() => {
  return {
    chart: {
      type: 'area',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    dataLabels: { enabled: false },
    colors: ['#00BAD1', '#FF9F43'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      position: 'bottom',
      formatter(seriesName: string, opts: any) {
        return `<span style="color:${opts.w.globals.colors[opts.seriesIndex]}">${seriesName}</span>`
      },
      itemMargin: {
        horizontal: 5,
        vertical: 0,
      },
      markers: {
        size: 4,
      },
    },
    fill: {
      type: 'gradient',
      gradient: {
        type: 'vertical',
        opacityFrom: 0.3,
        opacityTo: 0.3,
        stops: [0, 100],
      },
    },
    tooltip: {
      custom(data: any) {
        console.log('data >', data)

        return `<div class='bar-chart pa-2'>
          <div>上行：${data.series[0][data.dataPointIndex]}</div>
          <div>下行：${data.series[1][data.dataPointIndex]}</div>
        </div>`
      },
    },
    grid: {
      padding: { top: -10 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 0,
      strokeWidth: 0,
      strokeOpacity: 1,

      // strokeColors: ['#24B364', '#E64449'],
      // colors: ['#fff', '#fff'],
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: [
        '7/12',
        '8/12',
        '9/12',
        '10/12',
        '11/12',
        '12/12',
        '13/12',
        '14/12',
        '15/12',
        '16/12',
        '17/12',
        '18/12',
        '19/12',
        '20/12',
        '21/12',
      ],
    },
  }
})

const series = [
  {
    name: '上行',
    data: [280, 200, 220, 180, 270, 250, 70, 90, 200, 150, 160, 100, 150, 100, 50],
  },
  {
    name: '下行',
    data: [120, 100, 80, 90, 70, 60, 50, 40, 30, 20, 10, 0, 0, 0, 0],
  },
]

const timeOutChartConfig = computed(() => {
  return {
    chart: {
      type: 'area',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    dataLabels: { enabled: false },
    colors: ['#28C76F'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      position: 'bottom',
      showForSingleSeries: true,
      formatter(seriesName: string, opts: any) {
        return `<span>${seriesName}</span>`
      },
      itemMargin: {
        horizontal: 5,
        vertical: 0,
      },
      markers: {
        size: 4,
      },
    },
    fill: {
      type: 'gradient',
      gradient: {
        type: 'vertical',
        opacityFrom: 0.3,
        opacityTo: 0.3,
        stops: [0, 100],
      },
    },
    tooltip: {
      custom(data: any) {
        return `<div class='bar-chart pa-2'>
          <div>${data.series[0][data.dataPointIndex]}</div>
        </div>`
      },
    },
    grid: {
      padding: { top: -10 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 0,
      strokeWidth: 0,
      strokeOpacity: 1,

      // strokeColors: ['#24B364', '#E64449'],
      // colors: ['#fff', '#fff'],
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: [
        '7/12',
        '8/12',
        '9/12',
        '10/12',
        '11/12',
        '12/12',
        '13/12',
        '14/12',
        '15/12',
        '16/12',
        '17/12',
        '18/12',
        '19/12',
        '20/12',
        '21/12',
      ],
    },
  }
})

const timeOutSeries = [
  {
    name: '单位:ms',
    data: [280, 200, 220, 180, 270, 250, 70, 90, 200, 150, 160, 100, 150, 100, 50],
  },
]

const clientInfo = ref<TerminalInfo>({
  mac: '',
  rssi: 0,
  tx_rate: 0,
  rx_rate: 0,
  access_way: '',
  snr: '',
  chan: 0,
  ht: '',
  max_rate: '',
  assoc_time: '',
  connect_time: '',
  ip: '',
  name: '',
  ssid: '',
})

function getTerminalInfo() {
  $post('', { requestType: 123, data: { mac } }).then((res: any) => {
    if (res.msg === 'success')
      clientInfo.value = res.info.client_info as TerminalInfo

    // 这里可以处理获取到的终端信息
    else
      console.error('获取终端信息失败:', res.err_msg)
  }).catch((error: any) => {
    console.error('获取终端信息失败:', error)
  })
}

let mac = ''
const route = useRoute()

onMounted(() => {
  route.query.mac && (mac = route.query.mac as string)
  if (mac)
    getTerminalInfo()
})
</script>

<template>
  <div class="terminal-detail">
    <VCard class="mb-6">
      <template #title>
        <div class="mr-2 text-h5">
          终端名
        </div>
      </template>
      <VCardText>
        <div class="bg-grey-50 pa-4">
          <div class="text-button mb-2">
            终端信息
          </div>
          <VRow>
            <VCol>
              <div class="label">
                状态
              </div>
              <div class="value text-success">
                在线
              </div>
            </VCol>
            <VCol>
              <div class="label">
                接入类型
              </div>
              <div class="value">
                {{ clientInfo.access_way || '未知' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                厂商
              </div>
              <div class="value">
                --
              </div>
            </VCol>
            <VCol>
              <div class="label">
                IP地址
              </div>
              <div class="value">
                {{ clientInfo.ip || '--' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                MAC地址
              </div>
              <div class="value">
                {{ clientInfo.mac || '--' }}
              </div>
            </VCol>
          </VRow>
          <VRow>
            <VCol>
              <div class="label">
                上线时间
              </div>
              <div class="value">
                {{ clientInfo.connect_time || '--' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                在线时间
              </div>
              <div class="value">
                {{ clientInfo.assoc_time || '--' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                连接的SSID
              </div>
              <div class="value">
                {{ clientInfo.ssid || '--' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                设备名称
              </div>
              <div class="value">
                {{ clientInfo.name || '--' }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                信道/带宽
              </div>
              <div class="value">
                {{ clientInfo.chan || '--' }}/{{ clientInfo.ht || '--' }}
              </div>
            </VCol>
          </VRow>
        </div>
      </VCardText>
    </VCard>

    <VRow class="mb-6">
      <VCol>
        <VCard class="pa-6">
          <div class="device-info-item d-flex align-center justify-space-between">
            <div>
              <div class="item-value">
                {{ clientInfo.tx_rate || '--' }}
              </div>
              <div class="item-label">
                上行速率
              </div>
            </div>
            <div class="item-icon d-flex align-center justify-center primary">
              <VIcon
                color="primary"
                icon="tabler-arrow-narrow-up"
              />
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol>
        <VCard class="pa-6">
          <div class="device-info-item d-flex align-center justify-space-between">
            <div>
              <div class="item-value">
                {{ clientInfo.rx_rate || '--' }}
              </div>
              <div class="item-label">
                下行速率
              </div>
            </div>
            <div class="item-icon d-flex align-center justify-center success">
              <VIcon
                color="success"
                icon="tabler-arrow-narrow-up"
              />
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol>
        <VCard class="pa-6">
          <div class="device-info-item d-flex align-center justify-space-between">
            <div>
              <div class="item-value">
                {{ clientInfo.max_rate || '--' }}
              </div>
              <div class="item-label">
                协商速率Kbps
              </div>
            </div>
            <div class="item-icon d-flex align-center justify-center error">
              <VIcon
                color="error"
                icon="tabler-arrow-narrow-down"
              />
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol>
        <VCard class="pa-6">
          <div class="device-info-item d-flex align-center justify-space-between">
            <div>
              <div class="item-value">
                {{ clientInfo.rssi || '--' }}
              </div>
              <div class="item-label">
                信号强度dBm
              </div>
            </div>
            <div class="item-icon d-flex align-center justify-center warning">
              <VIcon
                color="warning"
                icon="tabler-git-compare"
              />
            </div>
          </div>
        </VCard>
      </VCol>
      <VCol>
        <VCard class="pa-6">
          <div class="device-info-item d-flex align-center justify-space-between">
            <div>
              <div class="item-value">
                {{ clientInfo.snr || '--' }}
              </div>
              <div class="item-label">
                信噪比 dB
              </div>
            </div>
            <div class="item-icon d-flex align-center justify-center info">
              <VIcon
                color="info"
                icon="tabler-antenna-bars-5"
              />
            </div>
          </div>
        </VCard>
      </VCol>
    </VRow>

    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="d-flex align-center">
            <div class="mr-4 text-h5">
              事件列表
            </div>
            <BtnGroupSelector
              class="mr-4"
              :options="EVENT_STATUS"
            />
            <BtnGroupSelector
              :options="NETWORK_EVENT_LEVEL"
              item-title="title"
            />
          </div>
          <VBtn
            variant="tonal"
            color="secondary"
          >
            {{ t('NetworkDeviceDetail.NoMoreRemind') }}
          </VBtn>
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #item.status="{ item }">
          <VChip
            variant="outlined"
            :color="eventClass[NETWORK_EVENT_LEVEL[item.status].value]"
          >
            {{
              NETWORK_EVENT_LEVEL[item.status].title }}
          </VChip>
        </template>
        <template #item.detail="{ item }">
          <VBtn
            variant="text"
            color="primary"
          >
            标记不解决
          </VBtn>
          <VBtn
            variant="text"
            color="primary"
          >
            不在提醒
          </VBtn>
          <VBtn
            variant="text"
            color="primary"
          >
            详情
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
            :show-meta="true"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <VRow>
      <VCol>
        <VCard>
          <template #title>
            <div class="mr-4 text-h5">
              上/下行总流量
            </div>
          </template>
          <VCardText>
            <VueApexCharts
              class="custom-area-chart"
              type="area"
              height="250"
              :options="balanceChartConfig"
              :series="series"
            />
          </VCardText>
        </VCard>
      </VCol>
      <VCol>
        <VCard>
          <template #title>
            <div class="mr-4 text-h5">
              时延
            </div>
          </template>
          <VCardText>
            <VueApexCharts
              class="custom-area-chart"
              type="area"
              height="250"
              :options="timeOutChartConfig"
              :series="timeOutSeries"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>

<style lang="scss" scoped>
.terminal-detail {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }

  .device-info-item {
    .item-value {
      color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
      font-size: 18px;
      font-weight: 500;
    }

    .item-label {
      color: rgba($color: var(--v-theme-on-surface), $alpha: 70%);
      font-size: 15px;
    }

    .item-icon {
      border-radius: 6px;
      block-size: 42px;
      inline-size: 42px;

      &.primary {
        background-color: rgba($color: var(--v-theme-primary), $alpha: 16%);
      }

      &.success {
        background-color: rgba($color: var(--v-theme-success), $alpha: 16%);
      }

      &.error {
        background-color: rgba($color: var(--v-theme-error), $alpha: 16%);
      }

      &.warning {
        background-color: rgba($color: var(--v-theme-warning), $alpha: 16%);
      }

      &.info {
        background-color: rgba($color: var(--v-theme-info), $alpha: 16%);
      }
    }
  }
}
</style>

<style lang="scss">
.custom-area-chart {
  .apexcharts-legend {
    &-series {
      border: 1px solid rgba($color: var(--v-theme-on-surface), $alpha: 12%);
      border-radius: 6px;
      padding-block: 3px;
      padding-inline: 15px;
    }
  }
}
</style>
