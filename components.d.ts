/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AcCpu: typeof import('./src/components/system/statusMonitor/acCpu.vue')['default']
    AcInfo: typeof import('./src/components/system/statusMonitor/acInfo.vue')['default']
    AcMemory: typeof import('./src/components/system/statusMonitor/acMemory.vue')['default']
    AcNetwork: typeof import('./src/components/system/statusMonitor/acNetwork.vue')['default']
    AddAuthenticatorAppDialog: typeof import('./src/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddDevice: typeof import('./src/components/deviceList/addDevice.vue')['default']
    AddEditAddressDialog: typeof import('./src/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./src/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./src/components/dialogs/AddEditRoleDialog.vue')['default']
    AddPaymentMethodDialog: typeof import('./src/components/dialogs/AddPaymentMethodDialog.vue')['default']
    AnalyticsCount: typeof import('./src/components/analytics/AnalyticsCount.vue')['default']
    AnalyticsCountCloud: typeof import('./src/components/analytics/AnalyticsCountCloud.vue')['default']
    AnalyticsProjectTable: typeof import('./src/components/analytics/AnalyticsProjectTable.vue')['default']
    AnalyticsProjectTableNine: typeof import('./src/components/analytics/AnalyticsProjectTableNine.vue')['default']
    AnalyticsProjectTableThree: typeof import('./src/components/analytics/AnalyticsProjectTableThree.vue')['default']
    AnalyticsSupportTracker: typeof import('./src/components/analytics/AnalyticsSupportTracker.vue')['default']
    ApexChartAreaChart: typeof import('./src/components/analytics/ApexChartAreaChart.vue')['default']
    ApLoad: typeof import('./src/components/network/ApLoad.vue')['default']
    AppAutocomplete: typeof import('./src/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./src/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./src/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./src/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./src/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./src/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/@core/components/app-form-elements/AppTextField.vue')['default']
    BtnGroupSelector: typeof import('./src/components/network/BtnGroupSelector.vue')['default']
    BtnGroupSelectorTwo: typeof import('./src/components/network/BtnGroupSelectorTwo.vue')['default']
    BuyNow: typeof import('./src/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./src/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    ConfirmDialog: typeof import('./src/components/dialogs/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./src/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DateSelector: typeof import('./src/components/network/DateSelector.vue')['default']
    DhcpList: typeof import('./src/components/system/list/dhcpList.vue')['default']
    DhcpPage: typeof import('./src/components/system/networkConfig/dhcpPage.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./src/@core/components/DropZone.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    IPPage: typeof import('./src/components/system/networkConfig/IPPage.vue')['default']
    IPTable: typeof import('./src/components/system/list/IPTable.vue')['default']
    LanPage: typeof import('./src/components/system/networkConfig/lanPage.vue')['default']
    Modal: typeof import('./src/components/modal/index.vue')['default']
    MoreAdd: typeof import('./src/components/deviceList/moreAdd.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    NetworkPage: typeof import('./src/components/system/networkConfig/networkPage.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    PaymentProvidersDialog: typeof import('./src/components/dialogs/PaymentProvidersDialog.vue')['default']
    PortPage: typeof import('./src/components/system/networkConfig/portPage.vue')['default']
    PortTable: typeof import('./src/components/system/list/PortTable.vue')['default']
    PricingPlanDialog: typeof import('./src/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./src/@core/components/ProductDescriptionEditor.vue')['default']
    RadioFrequency: typeof import('./src/components/network/RadioFrequency.vue')['default']
    ReferAndEarnDialog: typeof import('./src/components/dialogs/ReferAndEarnDialog.vue')['default']
    Reset: typeof import('./src/components/system/systemConfig/reset.vue')['default']
    Restart: typeof import('./src/components/system/systemConfig/restart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Safe: typeof import('./src/components/system/systemConfig/safe.vue')['default']
    ScanLocal: typeof import('./src/components/deviceList/scanLocal.vue')['default']
    ScrollToTop: typeof import('./src/@core/components/ScrollToTop.vue')['default']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    System: typeof import('./src/components/system/systemConfig/system.vue')['default']
    TablePagination: typeof import('./src/@core/components/TablePagination.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./src/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./src/components/dialogs/TwoFactorAuthDialog.vue')['default']
    Update: typeof import('./src/components/system/systemConfig/update.vue')['default']
    UserInfoEditDialog: typeof import('./src/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/components/dialogs/UserUpgradePlanDialog.vue')['default']
    VueApexCharts: typeof import('vue3-apexcharts')['default']
    WanList: typeof import('./src/components/system/networkConfig/wanList.vue')['default']
    WanPage: typeof import('./src/components/system/networkConfig/wanPage.vue')['default']
    Wireless: typeof import('./src/components/network/Wireless.vue')['default']
  }
}
